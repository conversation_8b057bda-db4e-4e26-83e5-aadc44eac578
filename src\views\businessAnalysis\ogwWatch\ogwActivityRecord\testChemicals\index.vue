<template>
  <div class="testChemicals">
    <div class="search-box">
      <OgwSearch
        :fields="searchFields"
        v-model="searchForm"
        @search="handleSearch"
      ></OgwSearch>
      <div class="btn-group">
        <el-button type="primary" @click="addRow">新增</el-button>
      </div>
    </div>
    <div class="table-container">
      <OgwTable
        :columns="columns"
        :data="tableData"
        :merge-keys="['deviceNameCode']"
        :show-index="true"
        :page-size="10"
        :current-page="1"
        :showActions="true"
        @cell-click="handleCellClick"
      >
        <template #state="{ row, $index }">
          <div v-if="row.state === 1" class="saved">已保存</div>
          <div v-else-if="row.state === 2" class="submitted">已提交</div>
          <div v-else>未保存</div>
        </template>

        <template #actions="{ row, $index }">
          <el-button type="text" size="mini" @click="saveRow(row)"
            >保存</el-button
          >
          <el-button type="text" size="mini" @click="deleteRow(row, $index)"
            >删除</el-button
          >
          <el-button type="text" size="mini" @click="submitRow(row)"
            >提交</el-button
          >
        </template>
      </OgwTable>
    </div>
  </div>
</template>
<script>
import OgwSearch from "@/components/comTable/OgwSearch.vue";
import OgwTable from "@/components/comTable/OgwTable.vue";
import {
  addTestChemical,
  getTestChemicals,
  saveTestChemical,
  deleteTestChemical,
} from "@/api/ogwActiveRecord/testChemicals.js";
import { getProd } from "@/api/common.js";

export default {
  name: "testChemicals",
  components: {
    OgwSearch,
    OgwTable,
  },
  created() {
    const dateArr = [];
    dateArr[0] = new Date().getFullYear() + "-" + Number(1);
    dateArr[1] = new Date().getFullYear() + "-" + new Date().getMonth();
    this.searchForm.monthRange = dateArr;
  },
  mounted() {
    this.getTableData();
    this.getProdList();
  },
  watch: {
    searchForm: {
      handler(val) {
        this.deviceOptions = this.orgList.map((item) => {
          if (item.orgId === val.orgId) {
            return item.children;
          }
        });
        this.searchFields[1].options = this.deviceOptions.flat(Infinity);
      },
      deep: true,
    },
  },
  computed: {
    searchFields() {
      return [
        {
          label: "组织机构:",
          prop: "orgId",
          type: "select",
          options: this.orgList.map((item) => {
            return {
              label: item.orgName,
              value: item.orgId,
            };
          }),
        },
        {
          label: "平台名称:",
          prop: "deviceNameCode",
          type: "select",
          options: [],
        },
        {
          prop: "monthRange",
          type: "monthrange",
          startPlaceholder: "开始月份",
          endPlaceholder: "结束月份",
        },
      ];
    },
  },
  data() {
    return {
      orgList: [],
      deviceOptions: [],
      platSelOptions: [],
      searchForm: {
        deptId: "",
        deviceNameCode: "",
        monthRange: ["", ""],
      },
      columns: [
        { label: "平台名称", prop: "productionName", editable: true,options:[] },
        { label: "项目名称", prop: "projectName", editable: true },
        { label: "供应商", prop: "supplier", editable: true },
        {
          label: "服务时间",
          prop: "serviceTime",
          editable: true,
          formatter: (row) => {
            return row?.serviceTime?.split(" ")[0];
          },
        },
        { label: "状态", prop: "state" },
        { label: "创建时间", prop: "createTime" },
      ],
      tableData: [],
    };
  },
  methods: {
    handleCellClick({ row, prop, index, value }) {
      console.log(`点击了第 ${index + 1} 行的 ${prop} 列，值为：`, value);
      // 可执行跳转、弹窗、编辑等操作
    },
    addRow() {
      // 新增空白行
      this.tableData.push({
        productionName: "",
        projectName: "",
        supplier: "",
        serviceTime: "",
        state: "",
      });
    },
    saveRow(row) {
      console.log("row", row);
      this.saveTableRow(row);
    },
    submitRow(row) {
      console.log("row", row);
      this.addTableRow(row);
    },
    deleteRow(row, index) {
      if (row.id) {
        this.deleteTableRow({ id: row.id });
      } else {
        this.tableData.splice(index, 1);
      }
    },
    handleSearch(value) {
      this.searchForm.deptId = value?.deptId;
      this.searchForm.deviceNameCode = value?.deviceNameCode;
      this.searchForm.monthRange = value.monthRange;
      this.getTableData();
    },
    async getTableData() {
      const data = {
        deptId: this.searchForm.deptId || null,
        productionUnitId: this.searchForm.deviceNameCode || null,
        startDate: this.searchForm.monthRange[0] || null,
        endDate: this.searchForm.monthRange[1] || null,
      };
      const res = await getTestChemicals(data);
      if (res.code === 200) {
        this.tableData = res.data;
      }
    },
    async saveTableRow(data) {
      const res = await saveTestChemical(data);
      if (res.code === 200) {
        this.$message.success("保存成功");
        this.getTableData();
      }
    },
    async addTableRow(data) {
      const res = await addTestChemical(data);
      if (res.code === 200) {
        this.$message.success("新增成功");
        this.getTableData();
      }
    },
    async deleteTableRow(data) {
      const res = await deleteTestChemical(data);
      if (res.code === 200) {
        this.$message.success("删除成功");
        this.getTableData();
      }
    },
    async getProdList() {
      const res = await getProd();
      if (res.code === 200) {
        this.orgList = res.data.map((item) => ({
          orgId: item.orgId,
          orgName: item.orgName,
          children: item.children.map((child) => ({
            value: child.hzNo,
            label: child.deptName,
          })),
        }));
      }
      this.platSelOptions = res.data.map((item) => {
        return item.children.map((child) => {
          return { label: child.deptName, value: child.hzNo };
        });
      });
      this.columns[0].options = this.platSelOptions.flat();
    },
  },
};
</script>
<style lang="scss" scoped>
.testChemicals {
  .search-box {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .btn-group {
      margin-bottom: 10px;
      text-align: right;
    }
  }
  .table-container {
    padding: 10px;
  }
}

.submitted {
  color: #00b42a;
}

.saved {
  color: #1677ff;
}
</style>
