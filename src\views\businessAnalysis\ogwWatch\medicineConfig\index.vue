<template>
  <div class="medicine-config">
    <OgwSearch :fields="searchFields" v-model="searchForm"></OgwSearch>

    <div class="table-container">
      <div class="table-actions">
        <el-button type="primary" size="mini" @click="saveInfo">保存</el-button>
      </div>
      <OgwTable
        :columns="columns"
        :data="tableData"
        :merge-keys="['productionName']"
        :show-actions="false"
        @cell-change="handleChange"
      >
        <template #instruction="{ row, $index }">
          <el-button type="text" size="mini" @click="deleteRow(row)">
            上传</el-button
          >
          <el-button type="text" size="mini" @click="checkInfo(row)"
            >查看</el-button
          ></template
        >
      </OgwTable>
    </div>
    <FileUpload
      :visible.sync="showUpload"
      :upload-url="'/api/chemicalConfig/upload'"
      :file-types="['.pdf', '.png', '.jpg']"
      :maxSizeMB="10"
      :fileData="fileData"
      @uploadFile="uploadFile"
    />
  </div>
</template>
<script>
import OgwSearch from "@/components/comTable/OgwSearch.vue";
import OgwTable from "@/components/comTable/OgwTable.vue";
import {
  getTableList,
  updateMedicineConfig,
} from "@/api/ogwWatch/medicineConfig.js";
export default {
  name: "medicineConfig",
  components: {
    OgwSearch,
    OgwTable,
  },
  mounted() {
    window.document.documentElement.setAttribute("data-theme", "tint");
    this.getTableListInfo();
  },
  data() {
    return {
      showUpload: false,
      searchForm: {
        org: "",
        device: "",
      },
      searchFields: [
        {
          label: "组织机构:",
          prop: "org",
          type: "select",
          options: [
            { label: "陵水-崖城作业公司", value: "org1" },
            { label: "深海作业公司", value: "org2" },
          ],
        },
        {
          label: "平台名称:",
          prop: "device",
          type: "select",
          options: [
            { label: "全部", value: "" },
            { label: "深海一号", value: "sh1" },
            { label: "崖城13-1", value: "yc13" },
          ],
        },
      ],
      columns: [
        { label: "平台名称", prop: "productionName" },
        { label: "药剂名称", prop: "chemicalName" },
        { label: "药剂型号", prop: "chemicalType" },
        { label: "库存编码", prop: "materialCode" },
        { label: "推荐加注浓度(ppm)", prop: "reConcentration" ,editable: true},
        {
          label: "用途",
          prop: "purpose",
          editable: true,
          options: [
            { label: "原油处理", value: 1 },
            { label: "污水处理", value: 2 },
            { label: "注水处理", value: 3 },
            { label: "天然气处理", value: 4 },
            { label: "其他化学药品", value: 5 },
          ],
        },
        {
          label: "费用关注度",
          prop: "attention",
          editable: true,
          options: [
            { label: "重点关注", value: 1 },
            { label: "一般关注", value: 2 },
          ],
        },
        { label: "加注浓度(ppm)", prop: "concentration" },
        { label: "药剂说明书", prop: "instruction" },
      ],
      tableData: [],
      updateData: [], //表格更新的数据
      fileData: {},
    };
  },
  methods: {
    deleteRow(row) {
      this.showUpload = true;
      this.fileData = { id: row.id };
    },
    checkInfo(row) {
      const fileIds = row.fileInfo || null;

      this.$router.push({
        name: "opinionsPre",
        params: { fileIds },
      });
    },
    async getTableListInfo() {
      const res = await getTableList();
      if (res.code === 200) {
        this.tableData = res.data;
      }
    },
    async saveInfo() {
      // set集合转成数组
      const targetArr = Array.from(new Set(this.updateData));

      const targetObj = targetArr.map((item) => this.tableData[item]);

      const res = await updateMedicineConfig(targetObj);
      // console.log("res", res);
      if (res.code === 200) {
        this.$message.success("保存成功");
      }
    },
    handleChange(row) {
      this.updateData.push(row.index);
    },
    uploadFile(file) {
      console.log("file", file);
    },
  },
};
</script>
<style lang="scss" scoped>
.medicine-config {
  padding: 20px;
  background-color: #fff;
}
.table-container {
  margin-top: 20px;
  .table-actions {
    margin-bottom: 10px;
    text-align: right;
  }
}
</style>
