<template>
  <div class="reportAOpinions">
    <div class="content">
      <el-card class="upload-card">
        <div slot="header" class="btns">
          <el-upload
            class="upload-demo"
            action="/api/attachment/uploadFile"
            multiple
            :limit="3"
            :on-exceed="handleExceed"
            :on-success="handleSuccess"
            :file-list="fileList"
            :show-file-list="false"
          >
            <el-button type="primary" class="confirm-btn" v-if="id"
              >上传</el-button
            >
          </el-upload>
          <el-button type="primary" class="confirm-btn" @click="handleCancle"
            >取消</el-button
          >
          <el-button
            type="primary"
            class="confirm-btn"
            @click="handleSubmit"
            v-if="id"
            >确定</el-button
          >
        </div>
        <div class="file-list">
          <template v-if="files.length > 0">
            <div class="file-item" v-for="(file, index) in files" :key="index">
              <div class="file-icon">
                <div class="icon-placeholder"></div>
              </div>
              <div class="file-info">
                <div class="file-name">{{ file.originalName }}</div>
                <div class="file-meta">
                  <span>文件大小：{{ file.size }}</span>
                  <span>上传人：{{ file.createBy }}</span>
                  <span>上传时间：{{ file.createTime }}</span>
                </div>
              </div>
              <div class="file-actions">
                <div
                  class="action-btn download-btn"
                  @click="handleDownload(file)"
                  title="下载"
                ></div>
                <div
                  class="action-btn preview-btn"
                  @click="handlePreview(file)"
                  title="预览"
                ></div>
                <div
                  class="action-btn delete-btn"
                  @click="handleDelete(file, index)"
                  title="删除"
                ></div>
              </div>
            </div>
          </template>
          <div v-else>暂无文件</div>
        </div>
      </el-card>
    </div>
    <div class="preview-box">
      <pdf-viewer
        v-if="fileUrl && fileType === 'pdf'"
        :pdf-url="fileUrl"
      ></pdf-viewer>
      <word-viewer
        v-if="fileUrl && (fileType === 'docx' || fileType === 'doc')"
        :file-url="fileUrl"
        :file-type="fileType"
      ></word-viewer>
    </div>
  </div>
</template>
<script>
import PdfViewer from "@/components/common/pdfView.vue"; 
import WordViewer from "@/components/common/wordView.vue";
import {
  getFileList,
  downloadFileById,
  previewFileById,
} from "@/api/file/index.js";
import { saveFilterChemicals } from "@/api/ogwActiveRecord/chemicalsServe.js";
import { downFileUtil } from "@/utils/file.js";
export default {
  name: "applicationForm",
  components: {
    PdfViewer,
    WordViewer,
  },
  activated() {
    this.id = this.$route.params.id || null;
    const fileIds = this.$route.params.fileIds;
    this.formType = this.$route.params.type;
    this.files = [];
    this.fileUrl = null;
    if (fileIds) {
      this.getFile(fileIds);
    }
  },
  mounted() {
    window.document.documentElement.setAttribute("data-theme", "tint");
    this.id = this.$route.params.id || null;
    const fileIds = this.$route.params.fileIds;
    this.formType = this.$route.params.type;
    if (fileIds) {
      this.getFile(fileIds);
    }
  },
  data() {
    return {
      fileType: "",
      fileUrl: "",
      id: "",
      fileList: [],
      files: [],
      form: [],
      formType: null,
    };
  },
  methods: {
    handleSubmit() {
      const data = {
        id: this.id,
        reportFileId: this.form.join(","),
      };
      this.saveTableRow(data);
    },
    handleCancle() {
      this.$router.back();
    },
    handleExceed(files, fileList) {
      this.$message.warning("最多只能上传3个文件");
    },
    handleSuccess(response, file, fileList) {
      this.$message.success("上传成功");
      this.files.push(response.data);
      this.form.push(response.data.fileId);
    },
    async handleDownload(file) {
      const res = await downloadFileById(file.fileId);
      if (res) {
        downFileUtil(res, file.originalName);
      }
    },
    async handlePreview(file) {
      this.fileType = file.originalName.split(".").pop();
      const id = file.fileId;
      const res = await previewFileById(id);
      if (res.code === 200) {
        this.fileUrl = res.data;
      }
    },
    handleDelete(file, index) {
      console.log("删除文件:", file.name);
      this.$confirm("确认删除该文件吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.files.splice(index, 1);
          this.$message.success("删除成功");
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },
    async getFile(id) {
      const res = await getFileList(id);
      if (res.code === 200) {
        this.files = res.data;
      }
    },
    async saveTableRow(data) {
      let res = null;
      switch (this.formType) {
        case "filter":
          data.type = 0;
          res = await saveFilterChemicals(data);
          break;
        case "evaluate":
          data.type = 1;
          res = await saveFilterChemicals(data);
          break;
      }
      if (res.code === 200) {
        this.$message.success("保存成功");
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.reportAOpinions {
  background: #fff;
  display: flex;
  .preview-box {
    flex: 1;
    height: 100%;
    border-left: 1px solid #ccc;
  }
  .content {
    height: auto;
    flex: 1;
    // padding: 20px;
    .upload-card {
      max-width: 800px;
      height: 100%;
      margin: 0 auto;
      .btns {
        display: flex;
      }
      .confirm-btn {
        margin-left: 12px;
      }

      .device-section {
        padding: 0 20px;
      }

      .el-form-item {
        margin-bottom: 22px;
      }
    }
  }
}

.file-list {
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #ebeef5;
}

.file-item:last-child {
  border-bottom: none;
}

.file-icon {
  margin-right: 16px;
}

.icon-placeholder {
  width: 40px;
  height: 40px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 14px;
  color: #303133;
  margin-bottom: 8px;
}

.file-meta {
  font-size: 12px;
  color: #909399;
}

.file-meta span {
  margin-right: 15px;
}

.file-actions {
  flex-shrink: 0;
  margin-left: 15px;
  display: flex;
  gap: 12px;
}

.action-btn {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.download-btn {
  background-color: #e6f7ff;
  position: relative;
}

.preview-btn {
  background-color: #f6ffed;
  position: relative;
}

.delete-btn {
  background-color: #fff1f0;
  position: relative;
}
</style>
